| Supported Targets | ESP32 | ESP32-C2 | ESP32-C3 | ESP32-C5 | ESP32-C6 | ESP32-C61 | ESP32-H2 | ESP32-P4 | ESP32-S2 | ESP32-S3 |
| ----------------- | ----- | -------- | -------- | -------- | -------- | --------- | -------- | -------- | -------- | -------- |

# ESP32-S3 AI翻译器

基于ESP32-S3-DevKitC-1开发板的实时串口翻译系统，支持中英文双向翻译，具有OLED显示功能。

## 功能特性

- **实时串口接收**：通过UART接收文本数据，支持115200波特率
- **环形缓冲区管理**：高效的数据缓存和处理机制
- **AI翻译功能**：集成MyMemory翻译API，支持中英文自动检测和双向翻译
- **OLED显示**：实时显示翻译结果和系统状态
- **WiFi连接**：支持无线网络连接和自动重连
- **错误处理**：完善的重试机制和异常处理

## 硬件要求

- ESP32-S3-DevKitC-1开发板
- SSD1306 OLED显示屏（I2C接口）
- USB数据线（供电和编程）
- 串口设备（用于发送待翻译文本）

## 硬件连接

### OLED显示屏连接

ESP32-S3开发板与OLED显示屏的连接方式：

```text
      ESP32-S3                        OLED LCD (I2C)
+------------------+              +-------------------+
|               GND+--------------+GND                |
|                  |              |                   |
|               3V3+--------------+VCC                |
|                  |              |                   |
|            GPIO5 +--------------+SDA                |
|                  |              |                   |
|            GPIO4 +--------------+SCL                |
+------------------+              +-------------------+
```

### 串口连接

串口翻译功能使用UART1端口，连接方式：

```text
      ESP32-S3                        串口设备
+------------------+              +-------------------+
|                  |              |                   |
|               GND+--------------+GND                |
|                  |              |                   |
|         UART1_RX +--------------+TX                 |
|         UART1_TX +--------------+RX                 |
+------------------+              +-------------------+
```

**注意**：UART1的具体引脚可在`main/config.h`中配置，默认使用内部连接。

## 配置说明

### WiFi配置

在`main/config.h`文件中修改WiFi设置：

```c
#define WIFI_SSID "your_wifi_name"        // 修改为你的WiFi名称
#define WIFI_PASSWORD "your_wifi_password" // 修改为你的WiFi密码
```

### 翻译API配置

项目默认使用MyMemory免费翻译API，无需API密钥。如需使用其他翻译服务，可在`main/config.h`中修改：

```c
#define TRANSLATION_API_URL "https://api.mymemory.translated.net/get"
```

### 缓冲区配置

可根据需要调整缓冲区大小：

```c
#define INPUT_BUFFER_SIZE 1024    // 输入缓冲区大小
#define OUTPUT_BUFFER_SIZE 128    // 输出缓冲区大小
```

## 编译和烧录

### 环境准备

确保已安装ESP-IDF开发环境（推荐v5.4或更高版本）。

### 编译步骤

```bash
# 设置ESP-IDF环境
source ~/esp/esp-idf/export.sh

# 编译项目
idf.py build

# 烧录到设备
idf.py -p /dev/ttyUSB0 flash

# 监控串口输出
idf.py -p /dev/ttyUSB0 monitor
```

退出串口监控请按 `Ctrl+]`。

详细的ESP-IDF使用说明请参考[官方文档](https://docs.espressif.com/projects/esp-idf/en/latest/get-started/index.html)。

## 使用方法

### 基本操作

1. **设备启动**：上电后设备会自动连接WiFi，OLED屏幕显示连接状态
2. **发送文本**：通过串口发送待翻译的文本（以换行符结束）
3. **查看结果**：翻译结果会显示在OLED屏幕上，同时通过串口输出

### 支持的语言

- **中文 → 英文**：自动检测中文字符并翻译为英文
- **英文 → 中文**：自动检测英文字符并翻译为中文

### 命令格式

通过串口发送文本，格式如下：

```
Hello World
你好世界
How are you?
今天天气怎么样？
```

每行文本以换行符（\n）结束，系统会自动检测语言并进行翻译。

## 系统输出示例

```bash
I (308) TRANSLATOR: WiFi connected successfully, IP: *************
I (318) TRANSLATOR: Ring buffer initialized successfully, size: 1024
I (328) TRANSLATOR: Ring buffer initialized successfully, size: 128
I (338) TRANSLATOR: UART initialized successfully
I (448) TRANSLATOR: Translating (en->zh): Hello World
I (1248) TRANSLATOR: Translation result: 你好世界
I (1348) TRANSLATOR: Translating (zh->en): 你好世界
I (2148) TRANSLATOR: Translation result: Hello World
```

## 故障排除

### 常见问题

#### 1. WiFi连接失败
**现象**：OLED显示"WiFi Failed"
**解决方法**：
- 检查`main/config.h`中的WiFi账号密码是否正确
- 确认WiFi信号强度足够
- 重启设备重新尝试连接

#### 2. 翻译功能不工作
**现象**：串口有输入但无翻译结果
**解决方法**：
- 确认WiFi已连接成功
- 检查网络是否能访问外部API
- 查看串口日志中的错误信息

#### 3. OLED显示异常
**现象**：屏幕无显示或显示乱码
**解决方法**：
- 检查I2C连接线是否正确
- 确认OLED设备地址（默认0x3C）
- 检查供电是否稳定

#### 4. 串口通信问题
**现象**：无法接收串口数据
**解决方法**：
- 确认波特率设置为115200
- 检查串口线连接是否正确
- 确认发送的文本以换行符结束

### 性能优化

- **响应速度**：系统处理延迟已优化至10ms
- **内存使用**：缓冲区大小可根据需要调整
- **网络稳定性**：支持自动重连和重试机制

### 技术支持

如遇到技术问题，请检查：
1. ESP-IDF版本是否为v5.4或更高
2. 硬件连接是否正确
3. 配置文件是否正确修改
4. 网络连接是否稳定

更多技术问题请参考ESP-IDF官方文档或提交Issue。
