/*
 * SPDX-FileCopyrightText: 2021-2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "esp_err.h"
#include "esp_log.h"
#include "driver/i2c_master.h"
#include "esp_lvgl_port.h"
#include "lvgl.h"
#include "driver/gpio.h"
#include "hal/gpio_types.h"
#include "esp_intr_alloc.h"
#include "esp_wifi.h"
#include "esp_system.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_http_client.h"
#include "es8311_codec.h"
#include "cJSON.h"
#include "driver/uart.h"
#include "config.h"  // 统一配置管理


#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#include "esp_lcd_sh1107.h"
#else
#include "esp_lcd_panel_vendor.h"
#endif

static volatile short i = 0;
volatile short FLAG = 0;
static QueueHandle_t uart_queue;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////// Please update the following configuration according to your LCD spec //////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define EXAMPLE_LCD_PIXEL_CLOCK_HZ    LCD_PIXEL_CLOCK_HZ
#define EXAMPLE_PIN_NUM_SDA           LCD_PIN_SDA
#define EXAMPLE_PIN_NUM_SCL           LCD_PIN_SCL
#define EXAMPLE_PIN_NUM_RST           LCD_PIN_RST
#define EXAMPLE_I2C_HW_ADDR           LCD_I2C_ADDR

// The pixel number in horizontal and vertical
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
#define EXAMPLE_LCD_H_RES              LCD_WIDTH
#define EXAMPLE_LCD_V_RES              CONFIG_EXAMPLE_SSD1306_HEIGHT
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#define EXAMPLE_LCD_H_RES              64
#define EXAMPLE_LCD_V_RES              LCD_WIDTH
#endif
// Bit number used to represent command and parameter
#define EXAMPLE_LCD_CMD_BITS           LCD_CMD_BITS
#define EXAMPLE_LCD_PARAM_BITS         LCD_PARAM_BITS
#define WEBSOCKET_URI "wss://"


static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

static void example_lvgl_demo_ui(lv_obj_t *label, char *text)
{
    lv_obj_clean(label);
    lv_label_set_long_mode(label, LV_LABEL_LONG_SCROLL_CIRCULAR); /* Circular scroll */
    lv_label_set_text(label, text);
    /* Size of the screen (if you use rotation 90 or 270, please set disp->driver->ver_res) */
    lv_obj_set_width(label, LCD_WIDTH);
    lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 0);
}

typedef struct {
    char *buffer;
    int head;
    int tail;
    int size;
    SemaphoreHandle_t mutex;
} ring_buffer_t;

static ring_buffer_t input_buffer;
static ring_buffer_t output_buffer;

// System status tracking
static bool wifi_connected = false;
static int wifi_retry_count = 0;

// Initialize ring buffer
bool ring_buffer_init(ring_buffer_t *rb, int size) {
    if (rb == NULL || size <= 0) {
        ESP_LOGE(TAG, "Invalid ring buffer parameters");
        return false;
    }

    rb->buffer = (char *)malloc(size);
    if (rb->buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate ring buffer memory");
        return false;
    }

    rb->mutex = xSemaphoreCreateMutex();
    if (rb->mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create ring buffer mutex");
        free(rb->buffer);
        rb->buffer = NULL;
        return false;
    }

    rb->size = size;
    rb->head = 0;
    rb->tail = 0;

    ESP_LOGI(TAG, "Ring buffer initialized successfully, size: %d", size);
    return true;
}

// Free ring buffer resources
void ring_buffer_free(ring_buffer_t *rb) {
    if (rb == NULL) {
        return;
    }

    if (rb->mutex != NULL) {
        vSemaphoreDelete(rb->mutex);
        rb->mutex = NULL;
    }

    if (rb->buffer != NULL) {
        free(rb->buffer);
        rb->buffer = NULL;
    }

    rb->size = 0;
    rb->head = 0;
    rb->tail = 0;

    ESP_LOGI(TAG, "Ring buffer freed successfully");
}

// Write data to ring buffer
int ring_buffer_write(ring_buffer_t *rb, const char *data, int len) {
    if (rb == NULL || data == NULL || len <= 0 || rb->buffer == NULL) {
        return 0;
    }

    int count = 0;
    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);

    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer write timeout");
        return 0;
    }
    
    for (int i = 0; i < len; i++) {
        // Calculate next position
        int next = (rb->head + 1) % rb->size;

        // Buffer full check
        if (next == rb->tail) {
            ESP_LOGW(TAG, "Ring buffer full, dropped %d bytes", len - count);
            break;
        }

        rb->buffer[rb->head] = data[i];
        rb->head = next;
        count++;
    }
    
    xSemaphoreGive(rb->mutex);
    return count;
}

int ring_buffer_read(ring_buffer_t *rb, char *data, int max_len) {
    if (rb == NULL || data == NULL || max_len <= 0 || rb->buffer == NULL) {
        return 0;
    }

    int count = 0;
    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);

    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer read timeout");
        return 0;
    }
    
    for (int i = 0; i < max_len; i++) {
        // Buffer empty check
        if (rb->tail == rb->head) {
            break;
        }

        data[i] = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        count++;
    }
    
    xSemaphoreGive(rb->mutex);
    return count;
}

// Check if buffer has a complete message (ending with '\n')
bool ring_buffer_has_line(ring_buffer_t *rb) {
    if (rb == NULL || rb->buffer == NULL) {
        return false;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer has_line timeout");
        return false;
    }
    
    int i = rb->tail;
    bool found = false;
    
    while (i != rb->head) {
        if (rb->buffer[i] == '\n') {
            found = true;
            break;
        }
        i = (i + 1) % rb->size;
    }
    
    xSemaphoreGive(rb->mutex);
    return found;
}

// Read a complete line from buffer
int ring_buffer_read_line(ring_buffer_t *rb, char *data, int max_len) {
    if (rb == NULL || data == NULL || max_len <= 0 || rb->buffer == NULL) {
        return 0;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer read_line timeout");
        return 0;
    }
    
    int i = 0;
    while (i < max_len - 1 && rb->tail != rb->head) {
        char c = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        
        data[i++] = c;
        
        if (c == '\n') {
            break;
        }
    }
    
    data[i] = '\0';  // Null-terminate the string

    xSemaphoreGive(rb->mutex);
    return i;
}

// Get available space in ring buffer
int ring_buffer_available_space(ring_buffer_t *rb) {
    if (rb == NULL || rb->buffer == NULL) {
        return 0;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer available_space timeout");
        return 0;
    }

    int available;
    if (rb->head >= rb->tail) {
        available = rb->size - (rb->head - rb->tail) - 1;
    } else {
        available = rb->tail - rb->head - 1;
    }

    xSemaphoreGive(rb->mutex);
    return available;
}

// Get used space in ring buffer
int ring_buffer_used_space(ring_buffer_t *rb) {
    if (rb == NULL || rb->buffer == NULL) {
        return 0;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer used_space timeout");
        return 0;
    }

    int used;
    if (rb->head >= rb->tail) {
        used = rb->head - rb->tail;
    } else {
        used = rb->size - (rb->tail - rb->head);
    }

    xSemaphoreGive(rb->mutex);
    return used;
}

// Check if ring buffer is empty
bool ring_buffer_is_empty(ring_buffer_t *rb) {
    if (rb == NULL || rb->buffer == NULL) {
        return true;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer is_empty timeout");
        return true;
    }

    bool empty = (rb->head == rb->tail);

    xSemaphoreGive(rb->mutex);
    return empty;
}

// Check if ring buffer is full
bool ring_buffer_is_full(ring_buffer_t *rb) {
    if (rb == NULL || rb->buffer == NULL) {
        return true;
    }

    TickType_t timeout = pdMS_TO_TICKS(MUTEX_TIMEOUT_MS);
    if (xSemaphoreTake(rb->mutex, timeout) != pdTRUE) {
        ESP_LOGW(TAG, "Ring buffer is_full timeout");
        return true;
    }

    bool full = ((rb->head + 1) % rb->size == rb->tail);

    xSemaphoreGive(rb->mutex);
    return full;
}

// HTTP event handler
esp_err_t http_event_handler(esp_http_client_event_t *evt) {
    static char response_buffer[2048];
    static int response_len = 0;

    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            // Accumulate response data
            if (evt->data_len > 0 && response_len + evt->data_len < sizeof(response_buffer) - 1) {
                memcpy(response_buffer + response_len, evt->data, evt->data_len);
                response_len += evt->data_len;
                response_buffer[response_len] = '\0';
            }
            break;

        case HTTP_EVENT_ON_FINISH:
            // Process complete response
            if (response_len > 0) {
                ESP_LOGI(TAG, "Received response (%d bytes)", response_len);

                // Parse MyMemory API JSON response
                cJSON *root = cJSON_Parse(response_buffer);
                if (root) {
                    cJSON *response_data = cJSON_GetObjectItem(root, "responseData");
                    if (response_data) {
                        cJSON *translated_text = cJSON_GetObjectItem(response_data, "translatedText");
                        if (translated_text && cJSON_IsString(translated_text)) {
                            const char *translation = translated_text->valuestring;

                            // Check if output buffer has enough space
                            int available_space = ring_buffer_available_space(&output_buffer);
                            int needed_space = strlen(translation) + 1; // +1 for newline

                            if (available_space >= needed_space) {
                                // Store translated result in output buffer
                                int written = ring_buffer_write(&output_buffer, translation, strlen(translation));
                                ring_buffer_write(&output_buffer, "\n", 1);

                                ESP_LOGI(TAG, "Translation result: %s", translation);
                                ESP_LOGI(TAG, "Written %d bytes to output buffer", written);
                            } else {
                                ESP_LOGW(TAG, "Output buffer full, dropping translation result");
                                ESP_LOGW(TAG, "Available: %d, Needed: %d", available_space, needed_space);
                            }
                        } else {
                            ESP_LOGW(TAG, "No translated text found in response");
                        }
                    } else {
                        ESP_LOGW(TAG, "No response data found in JSON");
                    }
                    cJSON_Delete(root);
                } else {
                    ESP_LOGE(TAG, "Failed to parse JSON response: %s", response_buffer);
                }
            } else {
                ESP_LOGW(TAG, "Empty response received");
            }

            // Reset buffer for next request
            response_len = 0;
            break;

        case HTTP_EVENT_ERROR:
            ESP_LOGE(TAG, "HTTP event error");
            response_len = 0;
            break;

        default:
            break;
    }
    return ESP_OK;
}

// Detect if text is Chinese (simplified check)
bool is_chinese_text(const char *text) {
    if (text == NULL) return false;

    for (int i = 0; text[i] != '\0'; i++) {
        unsigned char c = (unsigned char)text[i];
        // Check for UTF-8 Chinese characters (basic range)
        if (c >= 0xE4 && c <= 0xE9) {
            return true;
        }
    }
    return false;
}

// Build translation request URL for MyMemory API
int build_translation_url(char *url, size_t url_size, const char *text, const char *from_lang, const char *to_lang) {
    char encoded_text[512];
    int encoded_len = 0;

    // Simple URL encoding for text
    for (int i = 0; text[i] != '\0' && encoded_len < sizeof(encoded_text) - 4; i++) {
        char c = text[i];
        if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || (c >= '0' && c <= '9') ||
            c == '-' || c == '_' || c == '.' || c == '~') {
            encoded_text[encoded_len++] = c;
        } else {
            encoded_len += snprintf(encoded_text + encoded_len, sizeof(encoded_text) - encoded_len,
                                  "%%%02X", (unsigned char)c);
        }
    }
    encoded_text[encoded_len] = '\0';

    return snprintf(url, url_size, "%s?q=%s&langpair=%s|%s",
                   TRANSLATION_API_URL, encoded_text, from_lang, to_lang);
}

void translate_text(const char *text) {
    if (text == NULL || strlen(text) == 0) {
        ESP_LOGW(TAG, "Empty text for translation");
        return;
    }

    // Check WiFi connection before attempting translation
    if (!wifi_connected) {
        ESP_LOGW(TAG, "WiFi not connected, skipping translation");
        return;
    }

    // Detect language and set translation direction
    bool is_chinese = is_chinese_text(text);
    const char *from_lang = is_chinese ? "zh" : "en";
    const char *to_lang = is_chinese ? "en" : "zh";

    char url[1024];
    if (build_translation_url(url, sizeof(url), text, from_lang, to_lang) < 0) {
        ESP_LOGE(TAG, "Failed to build translation URL");
        return;
    }

    ESP_LOGI(TAG, "Translating (%s->%s): %s", from_lang, to_lang, text);

    // Retry mechanism for HTTP requests
    for (int retry = 0; retry < MAX_RETRY_COUNT; retry++) {
        if (retry > 0) {
            ESP_LOGI(TAG, "Translation retry attempt %d/%d", retry + 1, MAX_RETRY_COUNT);
            vTaskDelay(pdMS_TO_TICKS(TRANSLATION_RETRY_DELAY_MS));
        }

        esp_http_client_config_t config = {
            .url = url,
            .event_handler = http_event_handler,
            .method = HTTP_METHOD_GET,
            .timeout_ms = HTTP_TIMEOUT_MS,
            .user_agent = API_USER_AGENT,
        };

        esp_http_client_handle_t client = esp_http_client_init(&config);
        if (client == NULL) {
            ESP_LOGE(TAG, "Failed to initialize HTTP client (retry %d)", retry + 1);
            continue;
        }

        esp_err_t err = esp_http_client_perform(client);
        if (err == ESP_OK) {
            int status_code = esp_http_client_get_status_code(client);
            ESP_LOGI(TAG, "HTTP GET Status = %d (retry %d)", status_code, retry + 1);

            if (status_code == 200) {
                esp_http_client_cleanup(client);
                return; // Success, exit retry loop
            } else {
                ESP_LOGW(TAG, "Translation API returned status: %d (retry %d)", status_code, retry + 1);
            }
        } else {
            ESP_LOGE(TAG, "HTTP GET request failed: %s (retry %d)", esp_err_to_name(err), retry + 1);
        }

        esp_http_client_cleanup(client);
    }

    ESP_LOGE(TAG, "Translation failed after %d retries", MAX_RETRY_COUNT);
}

static void uart_event_task(void *pvParameters) {
    uart_event_t event;
    QueueHandle_t uart_queue = (QueueHandle_t) pvParameters;
    uint8_t* temp_buf = (uint8_t*) malloc(UART_BUFFER_SIZE);
    
    while (1) {
        // Wait for UART event
        if (xQueueReceive(uart_queue, (void*)&event, portMAX_DELAY)) {
            switch (event.type) {
                case UART_DATA:
                    // Read the UART data
                    int len = uart_read_bytes(UART_PORT_NUM, temp_buf, event.size, portMAX_DELAY);
                    if (len > 0) {
                        // Write to input buffer
                        ring_buffer_write(&input_buffer, (char*)temp_buf, len);
                    }
                    break;
                default:
                    break;
            }
        }
    }
    
    free(temp_buf);
    vTaskDelete(NULL);
}


// Processing task - check for complete lines and translate
static void process_task(void *pvParameters) {
    char line_buf[MAX_LINE_LENGTH];
    
    while (1) {
        if (ring_buffer_has_line(&input_buffer)) {
            int len = ring_buffer_read_line(&input_buffer, line_buf, sizeof(line_buf));
            if (len > 0) {
                // Remove any trailing newline
                if (line_buf[len-1] == '\n') {
                    line_buf[len-1] = '\0';
                }
                
                ESP_LOGI(TAG, "Received: %s", line_buf);
                
                // Send for translation
                translate_text(line_buf);
            }
        }
        
        // Small delay to prevent CPU hogging
        vTaskDelay(pdMS_TO_TICKS(PROCESS_TASK_DELAY_MS));
    }
}

// Initialize UART
static void uart_init() {
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_BITS_NUM,
        .parity = UART_PARITY_MODE,
        .stop_bits = UART_STOP_BITS_NUM,
        .flow_ctrl = UART_FLOW_CTRL_MODE,
        .source_clk = UART_SCLK_APB,
    };
    
    // Install UART driver
    ESP_ERROR_CHECK(uart_driver_install(UART_PORT_NUM, UART_BUFFER_SIZE * 2, UART_BUFFER_SIZE * 2, UART_QUEUE_SIZE, &uart_queue, 0));
    ESP_ERROR_CHECK(uart_param_config(UART_PORT_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_PORT_NUM, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
}

static void buttom_interrupt(lv_obj_t *label)
{
    i += 1;
    if(i == 4)
        i = 0;
    if(i == 0)
        example_lvgl_demo_ui(label, "First Menu");
    else if(i == 1)    
        example_lvgl_demo_ui(label, "Second Menu");
    else if(i == 2)
        example_lvgl_demo_ui(label, "Third Menu");
    else if(i == 3)
        example_lvgl_demo_ui(label, "Fourth Menu");

}


void app_main(void)
{
    ESP_LOGI(TAG, "Initialize I2C bus");
    i2c_master_bus_handle_t i2c_bus = NULL;
    i2c_master_bus_config_t bus_config = {
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .glitch_ignore_cnt = 7,
        .i2c_port = I2C_BUS_PORT,
        .sda_io_num = EXAMPLE_PIN_NUM_SDA,
        .scl_io_num = EXAMPLE_PIN_NUM_SCL,
        .flags.enable_internal_pullup = true,
    };
    ESP_ERROR_CHECK(i2c_new_master_bus(&bus_config, &i2c_bus));

    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_panel_io_handle_t io_handle = NULL;
    esp_lcd_panel_io_i2c_config_t io_config = {
        .dev_addr = EXAMPLE_I2C_HW_ADDR,
        .scl_speed_hz = EXAMPLE_LCD_PIXEL_CLOCK_HZ,
        .control_phase_bytes = 1,               // According to SSD1306 datasheet
        .lcd_cmd_bits = EXAMPLE_LCD_CMD_BITS,   // According to SSD1306 datasheet
        .lcd_param_bits = EXAMPLE_LCD_CMD_BITS, // According to SSD1306 datasheet
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
        .dc_bit_offset = 6,                     // According to SSD1306 datasheet
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
        .dc_bit_offset = 0,                     // According to SH1107 datasheet
        .flags =
        {
            .disable_control_phase = 1,
        }
#endif
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_i2c(i2c_bus, &io_config, &io_handle));

    ESP_LOGI(TAG, "Install SSD1306 panel driver");
    esp_lcd_panel_handle_t panel_handle = NULL;
    esp_lcd_panel_dev_config_t panel_config = {
        .bits_per_pixel = 1,
        .reset_gpio_num = EXAMPLE_PIN_NUM_RST,
    };
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
    esp_lcd_panel_ssd1306_config_t ssd1306_config = {
        .height = EXAMPLE_LCD_V_RES,
    };
    panel_config.vendor_config = &ssd1306_config;
    ESP_ERROR_CHECK(esp_lcd_new_panel_ssd1306(io_handle, &panel_config, &panel_handle));
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ESP_ERROR_CHECK(esp_lcd_new_panel_sh1107(io_handle, &panel_config, &panel_handle));
#endif

    ESP_ERROR_CHECK(esp_lcd_panel_reset(panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel_handle, true));

#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color(panel_handle, true));
#endif

    ESP_LOGI(TAG, "Initialize LVGL");
    const lvgl_port_cfg_t lvgl_cfg = ESP_LVGL_PORT_INIT_CONFIG();
    lvgl_port_init(&lvgl_cfg);
   
    const lvgl_port_display_cfg_t disp_cfg = {
        .io_handle = io_handle,
        .panel_handle = panel_handle,
        .buffer_size = EXAMPLE_LCD_H_RES * EXAMPLE_LCD_V_RES,
        .double_buffer = true,
        .hres = EXAMPLE_LCD_H_RES,
        .vres = EXAMPLE_LCD_V_RES,
        .monochrome = true,
        .rotation = {
            .swap_xy = false,
            .mirror_x = true,
            .mirror_y = true,
        }
    };

    lv_disp_t *disp = lvgl_port_add_disp(&disp_cfg);


    /* Rotation of the screen */
    lv_disp_set_rotation(disp, LV_DISP_ROT_NONE);
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_t *label = lv_label_create(scr);
    
    gpio_config_t config;
    (&config)->intr_type = GPIO_INTR_POSEDGE;
    (&config)->mode = GPIO_MODE_INPUT;
    (&config)->pin_bit_mask = 1ULL << 10;       
    (&config)->pull_down_en = GPIO_PULLDOWN_DISABLE;
    (&config)->pull_up_en = GPIO_PULLUP_ENABLE;
    gpio_config(&config);

    gpio_install_isr_service(10);
    gpio_isr_handler_add(10, (void *)(&buttom_interrupt), (void *)label);

    
        esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    esp_netif_t *sta_netif = esp_netif_create_default_wifi_sta();
    assert(sta_netif);
    esp_netif_ip_info_t info;
    ESP_ERROR_CHECK(esp_netif_get_ip_info(sta_netif, &info));
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));

    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASSWORD,
        },
    };
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));

    // Register event handlers
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &event_handler, label));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &event_handler, label));

    ESP_ERROR_CHECK(esp_wifi_start());

    if (!ring_buffer_init(&input_buffer, INPUT_BUFFER_SIZE)) {
        ESP_LOGE(TAG, "Failed to initialize input buffer");
        return;
    }

    if (!ring_buffer_init(&output_buffer, OUTPUT_BUFFER_SIZE)) {
        ESP_LOGE(TAG, "Failed to initialize output buffer");
        ring_buffer_free(&input_buffer);  // Clean up already allocated buffer
        return;
    }
    
    // Initialize UART
    uart_init();
    
    // Create tasks
    xTaskCreate(uart_event_task, "uart_event_task", UART_TASK_STACK_SIZE, (void*)uart_queue, UART_TASK_PRIORITY, NULL);
    xTaskCreate(process_task, "process_task", PROCESS_TASK_STACK_SIZE, NULL, PROCESS_TASK_PRIORITY, NULL);
    
    ESP_LOGI(TAG, "System initialized and ready to receive data");
}
 
static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT) {
        switch (event_id) {
            case WIFI_EVENT_STA_START:
                ESP_LOGI(TAG, "WiFi station started");
                esp_wifi_connect();
                break;

            case WIFI_EVENT_STA_DISCONNECTED:
                wifi_connected = false;
                wifi_retry_count++;
                ESP_LOGW(TAG, "WiFi disconnected, retry count: %d", wifi_retry_count);

                if (wifi_retry_count < MAX_RETRY_COUNT) {
                    ESP_LOGI(TAG, "Retrying WiFi connection...");
                    vTaskDelay(pdMS_TO_TICKS(WIFI_RETRY_DELAY_MS));
                    esp_wifi_connect();
                } else {
                    ESP_LOGE(TAG, "WiFi connection failed after %d retries", MAX_RETRY_COUNT);
                    char* string = (char*) calloc(DISPLAY_STRING_SIZE, sizeof(char));
                    sprintf(string, "WiFi Failed\r\n");
                    example_lvgl_demo_ui((lv_obj_t *)arg, string);
                    free(string);
                }
                break;

            default:
                break;
        }
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        wifi_connected = true;
        wifi_retry_count = 0;

        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        char ip_str[IP_STRING_SIZE];
        sprintf(ip_str, IPSTR, IP2STR(&event->ip_info.ip));
        char* string = (char*) calloc(DISPLAY_STRING_SIZE, sizeof(char));
        sprintf(string, "WIFI IP : %s\r\n", ip_str);
        example_lvgl_demo_ui((lv_obj_t *)arg, string);
        free(string);

        ESP_LOGI(TAG, "WiFi connected successfully, IP: %s", ip_str);
    }
}