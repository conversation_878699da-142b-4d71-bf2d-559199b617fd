/*
 * SPDX-FileCopyrightText: 2021-2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "esp_err.h"
#include "esp_log.h"
#include "driver/i2c_master.h"
#include "esp_lvgl_port.h"
#include "lvgl.h"
#include "driver/gpio.h"
#include "hal/gpio_types.h"
#include "esp_intr_alloc.h"
#include "esp_wifi.h"
#include "esp_system.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_http_client.h"
#include "es8311_codec.h"
#include "cJSON.h"
#include "driver/uart.h"


#define TAG "TRANSLATOR"

#define UART_NUM UART_NUM_1
#define BUF_SIZE 1024
#define RING_BUFFER_SIZE 1024
#define RESULT_BUFFER_SIZE 128


#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#include "esp_lcd_sh1107.h"
#else
#include "esp_lcd_panel_vendor.h"
#endif

static const char *TAG = "example";
static volatile short i = 0;
volatile short FLAG = 0;
#define I2C_BUS_PORT  0

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////// Please update the following configuration according to your LCD spec //////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define EXAMPLE_LCD_PIXEL_CLOCK_HZ    (400 * 1000)
#define EXAMPLE_PIN_NUM_SDA           5
#define EXAMPLE_PIN_NUM_SCL           4
#define EXAMPLE_PIN_NUM_RST           -1
#define EXAMPLE_I2C_HW_ADDR           0x3C

// The pixel number in horizontal and vertical
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
#define EXAMPLE_LCD_H_RES              128
#define EXAMPLE_LCD_V_RES              CONFIG_EXAMPLE_SSD1306_HEIGHT
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#define EXAMPLE_LCD_H_RES              64
#define EXAMPLE_LCD_V_RES              128
#endif
// Bit number used to represent command and parameter
#define EXAMPLE_LCD_CMD_BITS           8
#define EXAMPLE_LCD_PARAM_BITS         8
#define WEBSOCKET_URI "wss://"


static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

static void example_lvgl_demo_ui(lv_obj_t *label, char *text)
{
    lv_obj_clean(label);
    lv_label_set_long_mode(label, LV_LABEL_LONG_SCROLL_CIRCULAR); /* Circular scroll */
    lv_label_set_text(label, text);
    /* Size of the screen (if you use rotation 90 or 270, please set disp->driver->ver_res) */
    lv_obj_set_width(label, 128);
    lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 0);
}

typedef struct {
    char *buffer;
    int head;
    int tail;
    int size;
    SemaphoreHandle_t mutex;
} ring_buffer_t;

static ring_buffer_t input_buffer;
static ring_buffer_t output_buffer;
static const char *translation_api_url = "https://your-translation-api-url.com/translate";

// Initialize ring buffer
void ring_buffer_init(ring_buffer_t *rb, int size) {
    rb->buffer = (char *)malloc(size);
    if (rb->buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate ring buffer");
        return;
    }
    
    rb->size = size;
    rb->head = 0;
    rb->tail = 0;
    rb->mutex = xSemaphoreCreateMutex();
}

// Write data to ring buffer
int ring_buffer_write(ring_buffer_t *rb, const char *data, int len) {
    int i;
    int count = 0;
    
    if (xSemaphoreTake(rb->mutex, portMAX_DELAY) != pdTRUE) {
        return 0;
    }
    
    for (i = 0; i < len; i++) {
        // Calculate next position
        int next = (rb->head + 1) % rb->size;
        
        // Buffer full check
        if (next == rb->tail) {
            break;
        }
        
        rb->buffer[rb->head] = data[i];
        rb->head = next;
        count++;
    }
    
    xSemaphoreGive(rb->mutex);
    return count;
}

int ring_buffer_read(ring_buffer_t *rb, char *data, int max_len) {
    int i;
    int count = 0;
    
    if (xSemaphoreTake(rb->mutex, portMAX_DELAY) != pdTRUE) {
        return 0;
    }
    
    for (i = 0; i < max_len; i++) {
        // Buffer empty check
        if (rb->tail == rb->head) {
            break;
        }
        
        data[i] = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        count++;
    }
    
    xSemaphoreGive(rb->mutex);
    return count;
}

// Check if buffer has a complete message (ending with '\n')
bool ring_buffer_has_line(ring_buffer_t *rb) {
    if (xSemaphoreTake(rb->mutex, portMAX_DELAY) != pdTRUE) {
        return false;
    }
    
    int i = rb->tail;
    bool found = false;
    
    while (i != rb->head) {
        if (rb->buffer[i] == '\n') {
            found = true;
            break;
        }
        i = (i + 1) % rb->size;
    }
    
    xSemaphoreGive(rb->mutex);
    return found;
}

// Read a complete line from buffer
int ring_buffer_read_line(ring_buffer_t *rb, char *data, int max_len) {
    if (xSemaphoreTake(rb->mutex, portMAX_DELAY) != pdTRUE) {
        return 0;
    }
    
    int i = 0;
    while (i < max_len - 1 && rb->tail != rb->head) {
        char c = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        
        data[i++] = c;
        
        if (c == '\n') {
            break;
        }
    }
    
    data[i] = '\0';  // Null-terminate the string
    
    xSemaphoreGive(rb->mutex);
    return i;
}

// HTTP event handler
esp_err_t http_event_handler(esp_http_client_event_t *evt) {
    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            // Process translation response
            if (evt->data_len > 0) {
                // Parse JSON response - simplified example
                cJSON *root = cJSON_Parse(evt->data);
                if (root) {
                    cJSON *translated_text = cJSON_GetObjectItem(root, "translatedText");
                    if (translated_text && cJSON_IsString(translated_text)) {
                        // Store translated result in output buffer
                        ring_buffer_write(&output_buffer, translated_text->valuestring, 
                                         strlen(translated_text->valuestring));
                        ring_buffer_write(&output_buffer, "\n", 1);
                        ESP_LOGI(TAG, "Translation: %s", translated_text->valuestring);
                    }
                    cJSON_Delete(root);
                }
            }
            break;
        default:
            break;
    }
    return ESP_OK;
}

void translate_text(const char *text) {
    char post_data[512];
    snprintf(post_data, sizeof(post_data), "{\"text\":\"%s\",\"target_language\":\"en\"}", text);
    
    esp_http_client_config_t config = {
        .url = translation_api_url,
        .event_handler = http_event_handler,
        .method = HTTP_METHOD_POST,
    };
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    
    esp_http_client_set_header(client, "Content-Type", "application/json");
    esp_http_client_set_post_field(client, post_data, strlen(post_data));
    
    esp_err_t err = esp_http_client_perform(client);
    if (err == ESP_OK) {
        ESP_LOGI(TAG, "HTTP POST Status = %d", esp_http_client_get_status_code(client));
    } else {
        ESP_LOGE(TAG, "HTTP POST request failed: %s", esp_err_to_name(err));
    }
    
    esp_http_client_cleanup(client);
}

static void uart_event_task(void *pvParameters) {
    uart_event_t event;
    QueueHandle_t uart_queue = (QueueHandle_t) pvParameters;
    uint8_t* temp_buf = (uint8_t*) malloc(BUF_SIZE);
    
    while (1) {
        // Wait for UART event
        if (xQueueReceive(uart_queue, (void*)&event, portMAX_DELAY)) {
            switch (event.type) {
                case UART_DATA:
                    // Read the UART data
                    int len = uart_read_bytes(UART_NUM, temp_buf, event.size, portMAX_DELAY);
                    if (len > 0) {
                        // Write to input buffer
                        ring_buffer_write(&input_buffer, (char*)temp_buf, len);
                    }
                    break;
                default:
                    break;
            }
        }
    }
    
    free(temp_buf);
    vTaskDelete(NULL);
}


// Processing task - check for complete lines and translate
static void process_task(void *pvParameters) {
    char line_buf[512];
    
    while (1) {
        if (ring_buffer_has_line(&input_buffer)) {
            int len = ring_buffer_read_line(&input_buffer, line_buf, sizeof(line_buf));
            if (len > 0) {
                // Remove any trailing newline
                if (line_buf[len-1] == '\n') {
                    line_buf[len-1] = '\0';
                }
                
                ESP_LOGI(TAG, "Received: %s", line_buf);
                
                // Send for translation
                translate_text(line_buf);
            }
        }
        
        // Small delay to prevent CPU hogging
        vTaskDelay(pdMS_TO_TICKS(50));
    }
}

// Initialize UART
static void uart_init() {
    uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_APB,
    };
    
    // Install UART driver
    ESP_ERROR_CHECK(uart_driver_install(UART_NUM, BUF_SIZE * 2, BUF_SIZE * 2, 20, &uart_queue, 0));
    ESP_ERROR_CHECK(uart_param_config(UART_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_NUM, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
}

static void buttom_interrupt(lv_obj_t *label)
{
    i += 1;
    if(i == 4)
        i = 0;
    if(i == 0)
        example_lvgl_demo_ui(label, "First Menu");
    else if(i == 1)    
        example_lvgl_demo_ui(label, "Second Menu");
    else if(i == 2)
        example_lvgl_demo_ui(label, "Third Menu");
    else if(i == 3)
        example_lvgl_demo_ui(label, "Fourth Menu");

}


void app_main(void)
{
    ESP_LOGI(TAG, "Initialize I2C bus");
    i2c_master_bus_handle_t i2c_bus = NULL;
    i2c_master_bus_config_t bus_config = {
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .glitch_ignore_cnt = 7,
        .i2c_port = I2C_BUS_PORT,
        .sda_io_num = EXAMPLE_PIN_NUM_SDA,
        .scl_io_num = EXAMPLE_PIN_NUM_SCL,
        .flags.enable_internal_pullup = true,
    };
    ESP_ERROR_CHECK(i2c_new_master_bus(&bus_config, &i2c_bus));

    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_panel_io_handle_t io_handle = NULL;
    esp_lcd_panel_io_i2c_config_t io_config = {
        .dev_addr = EXAMPLE_I2C_HW_ADDR,
        .scl_speed_hz = EXAMPLE_LCD_PIXEL_CLOCK_HZ,
        .control_phase_bytes = 1,               // According to SSD1306 datasheet
        .lcd_cmd_bits = EXAMPLE_LCD_CMD_BITS,   // According to SSD1306 datasheet
        .lcd_param_bits = EXAMPLE_LCD_CMD_BITS, // According to SSD1306 datasheet
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
        .dc_bit_offset = 6,                     // According to SSD1306 datasheet
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
        .dc_bit_offset = 0,                     // According to SH1107 datasheet
        .flags =
        {
            .disable_control_phase = 1,
        }
#endif
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_i2c(i2c_bus, &io_config, &io_handle));

    ESP_LOGI(TAG, "Install SSD1306 panel driver");
    esp_lcd_panel_handle_t panel_handle = NULL;
    esp_lcd_panel_dev_config_t panel_config = {
        .bits_per_pixel = 1,
        .reset_gpio_num = EXAMPLE_PIN_NUM_RST,
    };
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
    esp_lcd_panel_ssd1306_config_t ssd1306_config = {
        .height = EXAMPLE_LCD_V_RES,
    };
    panel_config.vendor_config = &ssd1306_config;
    ESP_ERROR_CHECK(esp_lcd_new_panel_ssd1306(io_handle, &panel_config, &panel_handle));
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ESP_ERROR_CHECK(esp_lcd_new_panel_sh1107(io_handle, &panel_config, &panel_handle));
#endif

    ESP_ERROR_CHECK(esp_lcd_panel_reset(panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel_handle, true));

#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color(panel_handle, true));
#endif

    ESP_LOGI(TAG, "Initialize LVGL");
    const lvgl_port_cfg_t lvgl_cfg = ESP_LVGL_PORT_INIT_CONFIG();
    lvgl_port_init(&lvgl_cfg);
   
    const lvgl_port_display_cfg_t disp_cfg = {
        .io_handle = io_handle,
        .panel_handle = panel_handle,
        .buffer_size = EXAMPLE_LCD_H_RES * EXAMPLE_LCD_V_RES,
        .double_buffer = true,
        .hres = EXAMPLE_LCD_H_RES,
        .vres = EXAMPLE_LCD_V_RES,
        .monochrome = true,
        .rotation = {
            .swap_xy = false,
            .mirror_x = true,
            .mirror_y = true,
        }
    };

    lv_disp_t *disp = lvgl_port_add_disp(&disp_cfg);


    /* Rotation of the screen */
    lv_disp_set_rotation(disp, LV_DISP_ROT_NONE);
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_t *label = lv_label_create(scr);
    
    gpio_config_t config;
    (&config)->intr_type = GPIO_INTR_POSEDGE;
    (&config)->mode = GPIO_MODE_INPUT;
    (&config)->pin_bit_mask = 1ULL << 10;       
    (&config)->pull_down_en = GPIO_PULLDOWN_DISABLE;
    (&config)->pull_up_en = GPIO_PULLUP_ENABLE;
    gpio_config(&config);

    gpio_install_isr_service(10);
    gpio_isr_handler_add(10, (void *)(&buttom_interrupt), (void *)label);

    
        esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    esp_netif_t *sta_netif = esp_netif_create_default_wifi_sta();
    assert(sta_netif);
    esp_netif_ip_info_t info;
    ESP_ERROR_CHECK(esp_netif_get_ip_info(sta_netif, &info));
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));

    wifi_config_t wifi_config = {
        .sta = {
            .ssid = "203",
            .password = "203203203",
        },
    };
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));

    ESP_ERROR_CHECK(esp_wifi_start());
    ESP_ERROR_CHECK(esp_wifi_connect());
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &event_handler, label));

    ring_buffer_init(&input_buffer, RING_BUFFER_SIZE);
    ring_buffer_init(&output_buffer, RESULT_BUFFER_SIZE);
    
    // Initialize UART
    uart_init();
    
    // Create tasks
    xTaskCreate(uart_event_task, "uart_event_task", 4096, (void*)uart_queue, 5, NULL);
    xTaskCreate(process_task, "process_task", 8192, NULL, 4, NULL);
    
    ESP_LOGI(TAG, "System initialized and ready to receive data");
}
 
static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        char ip_str[16];
        sprintf(ip_str, IPSTR, IP2STR(&event->ip_info.ip));
        char* string = (char*) calloc(128, sizeof(char));
        sprintf(string, "WIFI IP : %s\r\n", ip_str);
        example_lvgl_demo_ui((lv_obj_t *)arg, string);
        free(string); // 释放分配的内存
    }
}