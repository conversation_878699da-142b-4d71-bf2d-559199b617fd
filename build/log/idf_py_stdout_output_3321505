[1/9] Performing build step for 'bootloader'
[1/1] cd /home/<USER>/ESP32/ai_led/build/bootloader/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.13_env/bin/python /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/ESP32/ai_led/build/bootloader/bootloader.bin
Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.
[2/9] No install step for 'bootloader'
[3/9] Completed 'bootloader'
[4/9] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/9] Linking C static library esp-idf/main/libmain.a
[6/9] Generating ld/sections.ld
[7/9] Linking CXX executable ai_led.elf
[8/9] Generating binary image from built executable
esptool.py v4.9.0
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /home/<USER>/ESP32/ai_led/build/ai_led.bin
[9/9] cd /home/<USER>/ESP32/ai_led/build/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.13_env/bin/python /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /home/<USER>/ESP32/ai_led/build/partition_table/partition-table.bin /home/<USER>/ESP32/ai_led/build/ai_led.bin
ai_led.bin binary size 0xfcb40 bytes. Smallest app partition is 0x659000 bytes. 0x55c4c0 bytes (84%) free.
