# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION ${CMAKE_VERSION}) # this file comes with cmake

# If CMAKE_DISABLE_SOURCE_CHANGES is set to true and the source directory is an
# existing directory in our source tree, calling file(MAKE_DIRECTORY) on it
# would cause a fatal error, even though it would be a no-op.
if(NOT EXISTS "/home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject")
  file(MAKE_DIRECTORY "/home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject")
endif()
file(MAKE_DIRECTORY
  "/home/<USER>/ESP32/ai_led/build/bootloader"
  "/home/<USER>/ESP32/ai_led/build/bootloader-prefix"
  "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/tmp"
  "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp"
  "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src"
  "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp"
)

set(configSubDirs )
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp${cfgdir}") # cfgdir has leading slash
endif()
