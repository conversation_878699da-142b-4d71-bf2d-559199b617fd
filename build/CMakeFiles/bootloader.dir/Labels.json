{"sources": [{"file": "/home/<USER>/ESP32/ai_led/build/CMakeFiles/bootloader"}, {"file": "/home/<USER>/ESP32/ai_led/build/CMakeFiles/bootloader.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/home/<USER>/ESP32/ai_led/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}