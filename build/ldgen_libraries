/home/<USER>/ESP32/ai_led/build/esp-idf/xtensa/libxtensa.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_pm/libesp_pm.a
/home/<USER>/ESP32/ai_led/build/esp-idf/mbedtls/libmbedtls.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_app_format/libesp_app_format.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a
/home/<USER>/ESP32/ai_led/build/esp-idf/app_update/libapp_update.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_partition/libesp_partition.a
/home/<USER>/ESP32/ai_led/build/esp-idf/efuse/libefuse.a
/home/<USER>/ESP32/ai_led/build/esp-idf/bootloader_support/libbootloader_support.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_mm/libesp_mm.a
/home/<USER>/ESP32/ai_led/build/esp-idf/spi_flash/libspi_flash.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_system/libesp_system.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_common/libesp_common.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_rom/libesp_rom.a
/home/<USER>/ESP32/ai_led/build/esp-idf/hal/libhal.a
/home/<USER>/ESP32/ai_led/build/esp-idf/log/liblog.a
/home/<USER>/ESP32/ai_led/build/esp-idf/heap/libheap.a
/home/<USER>/ESP32/ai_led/build/esp-idf/soc/libsoc.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_security/libesp_security.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_hw_support/libesp_hw_support.a
/home/<USER>/ESP32/ai_led/build/esp-idf/freertos/libfreertos.a
/home/<USER>/ESP32/ai_led/build/esp-idf/newlib/libnewlib.a
/home/<USER>/ESP32/ai_led/build/esp-idf/pthread/libpthread.a
/home/<USER>/ESP32/ai_led/build/esp-idf/cxx/libcxx.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_timer/libesp_timer.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_ringbuf/libesp_ringbuf.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_uart/libesp_driver_uart.a
/home/<USER>/ESP32/ai_led/build/esp-idf/app_trace/libapp_trace.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_event/libesp_event.a
/home/<USER>/ESP32/ai_led/build/esp-idf/nvs_flash/libnvs_flash.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_spi/libesp_driver_spi.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a
/home/<USER>/ESP32/ai_led/build/esp-idf/sdmmc/libsdmmc.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a
/home/<USER>/ESP32/ai_led/build/esp-idf/driver/libdriver.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_phy/libesp_phy.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_vfs_console/libesp_vfs_console.a
/home/<USER>/ESP32/ai_led/build/esp-idf/vfs/libvfs.a
/home/<USER>/ESP32/ai_led/build/esp-idf/lwip/liblwip.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_netif/libesp_netif.a
/home/<USER>/ESP32/ai_led/build/esp-idf/wpa_supplicant/libwpa_supplicant.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_coex/libesp_coex.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_wifi/libesp_wifi.a
/home/<USER>/ESP32/ai_led/build/esp-idf/unity/libunity.a
/home/<USER>/ESP32/ai_led/build/esp-idf/cmock/libcmock.a
/home/<USER>/ESP32/ai_led/build/esp-idf/console/libconsole.a
/home/<USER>/ESP32/ai_led/build/esp-idf/http_parser/libhttp_parser.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp-tls/libesp-tls.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_adc/libesp_adc.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_driver_cam/libesp_driver_cam.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_eth/libesp_eth.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_gdbstub/libesp_gdbstub.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_hid/libesp_hid.a
/home/<USER>/ESP32/ai_led/build/esp-idf/tcp_transport/libtcp_transport.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_http_client/libesp_http_client.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_http_server/libesp_http_server.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_https_ota/libesp_https_ota.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_https_server/libesp_https_server.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_lcd/libesp_lcd.a
/home/<USER>/ESP32/ai_led/build/esp-idf/protobuf-c/libprotobuf-c.a
/home/<USER>/ESP32/ai_led/build/esp-idf/protocomm/libprotocomm.a
/home/<USER>/ESP32/ai_led/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a
/home/<USER>/ESP32/ai_led/build/esp-idf/espcoredump/libespcoredump.a
/home/<USER>/ESP32/ai_led/build/esp-idf/wear_levelling/libwear_levelling.a
/home/<USER>/ESP32/ai_led/build/esp-idf/fatfs/libfatfs.a
/home/<USER>/ESP32/ai_led/build/esp-idf/json/libjson.a
/home/<USER>/ESP32/ai_led/build/esp-idf/mqtt/libmqtt.a
/home/<USER>/ESP32/ai_led/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a
/home/<USER>/ESP32/ai_led/build/esp-idf/perfmon/libperfmon.a
/home/<USER>/ESP32/ai_led/build/esp-idf/rt/librt.a
/home/<USER>/ESP32/ai_led/build/esp-idf/spiffs/libspiffs.a
/home/<USER>/ESP32/ai_led/build/esp-idf/touch_element/libtouch_element.a
/home/<USER>/ESP32/ai_led/build/esp-idf/usb/libusb.a
/home/<USER>/ESP32/ai_led/build/esp-idf/wifi_provisioning/libwifi_provisioning.a
/home/<USER>/ESP32/ai_led/build/esp-idf/espressif__esp_codec_dev/libespressif__esp_codec_dev.a
/home/<USER>/ESP32/ai_led/build/esp-idf/espressif__esp_lcd_sh1107/libespressif__esp_lcd_sh1107.a
/home/<USER>/ESP32/ai_led/build/esp-idf/lvgl__lvgl/liblvgl__lvgl.a
/home/<USER>/ESP32/ai_led/build/esp-idf/espressif__esp_lvgl_port/libespressif__esp_lvgl_port.a
/home/<USER>/ESP32/ai_led/build/esp-idf/espressif__esp_websocket_client/libespressif__esp_websocket_client.a
/home/<USER>/ESP32/ai_led/build/esp-idf/main/libmain.a
