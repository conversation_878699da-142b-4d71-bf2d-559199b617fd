{"write_flash_args": ["--flash_mode", "dio", "--flash_size", "8MB", "--flash_freq", "80m"], "flash_settings": {"flash_mode": "dio", "flash_size": "8MB", "flash_freq": "80m"}, "flash_files": {"0x0": "bootloader/bootloader.bin", "0x10000": "ai_led.bin", "0x8000": "partition_table/partition-table.bin"}, "bootloader": {"offset": "0x0", "file": "bootloader/bootloader.bin", "encrypted": "false"}, "app": {"offset": "0x10000", "file": "ai_led.bin", "encrypted": "false"}, "partition-table": {"offset": "0x8000", "file": "partition_table/partition-table.bin", "encrypted": "false"}, "extra_esptool_args": {"after": "hard_reset", "before": "default_reset", "stub": true, "chip": "esp32s3"}}