components:
  - name: "app_trace"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/app_trace"
  - name: "app_update"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/app_update"
  - name: "bootloader"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/bootloader"
  - name: "bootloader_support"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support"
  - name: "bt"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/bt"
  - name: "cmock"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/cmock"
  - name: "console"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/console"
  - name: "cxx"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/cxx"
  - name: "driver"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/driver"
  - name: "efuse"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/efuse"
  - name: "esp-tls"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp-tls"
  - name: "esp_adc"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_adc"
  - name: "esp_app_format"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format"
  - name: "esp_bootloader_format"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format"
  - name: "esp_coex"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_coex"
  - name: "esp_common"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_common"
  - name: "esp_driver_ana_cmpr"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr"
  - name: "esp_driver_cam"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam"
  - name: "esp_driver_dac"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac"
  - name: "esp_driver_gpio"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio"
  - name: "esp_driver_gptimer"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer"
  - name: "esp_driver_i2c"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c"
  - name: "esp_driver_i2s"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s"
  - name: "esp_driver_isp"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp"
  - name: "esp_driver_jpeg"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg"
  - name: "esp_driver_ledc"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc"
  - name: "esp_driver_mcpwm"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm"
  - name: "esp_driver_parlio"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio"
  - name: "esp_driver_pcnt"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt"
  - name: "esp_driver_ppa"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa"
  - name: "esp_driver_rmt"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt"
  - name: "esp_driver_sdio"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio"
  - name: "esp_driver_sdm"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm"
  - name: "esp_driver_sdmmc"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc"
  - name: "esp_driver_sdspi"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi"
  - name: "esp_driver_spi"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi"
  - name: "esp_driver_touch_sens"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens"
  - name: "esp_driver_tsens"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens"
  - name: "esp_driver_uart"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart"
  - name: "esp_driver_usb_serial_jtag"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag"
  - name: "esp_eth"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_eth"
  - name: "esp_event"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_event"
  - name: "esp_gdbstub"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub"
  - name: "esp_hid"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_hid"
  - name: "esp_http_client"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client"
  - name: "esp_http_server"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server"
  - name: "esp_https_ota"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota"
  - name: "esp_https_server"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server"
  - name: "esp_hw_support"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support"
  - name: "esp_lcd"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd"
  - name: "esp_local_ctrl"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl"
  - name: "esp_mm"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_mm"
  - name: "esp_netif"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_netif"
  - name: "esp_netif_stack"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack"
  - name: "esp_partition"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_partition"
  - name: "esp_phy"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_phy"
  - name: "esp_pm"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_pm"
  - name: "esp_psram"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_psram"
  - name: "esp_ringbuf"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf"
  - name: "esp_rom"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom"
  - name: "esp_security"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_security"
  - name: "esp_system"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_system"
  - name: "esp_timer"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_timer"
  - name: "esp_vfs_console"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console"
  - name: "esp_wifi"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi"
  - name: "espcoredump"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/espcoredump"
  - name: "esptool_py"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/esptool_py"
  - name: "fatfs"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/fatfs"
  - name: "freertos"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/freertos"
  - name: "hal"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/hal"
  - name: "heap"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/heap"
  - name: "http_parser"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/http_parser"
  - name: "idf_test"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/idf_test"
  - name: "ieee802154"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/ieee802154"
  - name: "json"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/json"
  - name: "linux"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/linux"
  - name: "log"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/log"
  - name: "lwip"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/lwip"
  - name: "mbedtls"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/mbedtls"
  - name: "mqtt"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/mqtt"
  - name: "newlib"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/newlib"
  - name: "nvs_flash"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash"
  - name: "nvs_sec_provider"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider"
  - name: "openthread"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/openthread"
  - name: "partition_table"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/partition_table"
  - name: "perfmon"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/perfmon"
  - name: "protobuf-c"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c"
  - name: "protocomm"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/protocomm"
  - name: "pthread"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/pthread"
  - name: "riscv"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/riscv"
  - name: "rt"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/rt"
  - name: "sdmmc"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/sdmmc"
  - name: "soc"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/soc"
  - name: "spi_flash"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/spi_flash"
  - name: "spiffs"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/spiffs"
  - name: "tcp_transport"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport"
  - name: "touch_element"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/touch_element"
  - name: "ulp"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/ulp"
  - name: "unity"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/unity"
  - name: "usb"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/usb"
  - name: "vfs"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/vfs"
  - name: "wear_levelling"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling"
  - name: "wifi_provisioning"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning"
  - name: "wpa_supplicant"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant"
  - name: "xtensa"
    path: "/home/<USER>/esp/v5.4.1/esp-idf/components/xtensa"
  - name: "main"
    path: "/home/<USER>/ESP32/ai_led/main"
