{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPath": "/home/<USER>/esp/v5.4.1/esp-idf", "idf.pythonInstallPath": "/usr/bin/python3", "idf.espAdfPath": "/home/<USER>/.espressif/esp-adf", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.port": "/dev/ttyUSB0", "idf.toolsPath": "/home/<USER>/.espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "/home/<USER>/.espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clangd", "clangd.arguments": ["--background-index", "--query-driver=/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc", "--compile-commands-dir=${workspaceFolder}/build"], "files.associations": {"i2c_master.h": "c", "esp_intr_alloc.h": "c"}, "idf.flashType": "UART"}